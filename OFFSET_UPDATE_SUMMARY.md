# CS2 偏移更新总结

## 更新时间
2025-08-07 06:57:19 UTC

## 更新内容

### 1. 构建系统修改
- 修改了 `build.rs` 文件，使其优先使用本地的 cs2-dumper 输出文件
- 如果本地文件存在，将使用本地文件而不是从远程下载
- 保持了向后兼容性，如果本地文件不存在仍会从远程下载

### 2. 偏移数据更新
使用了最新的 cs2-dumper 输出，包含以下关键偏移：

#### client.dll 偏移
- `dwCSGOInput`: 0x1D2C010 (30588944)
- `dwEntityList`: 0x1CBE620 (30139936)
- `dwGameEntitySystem`: 0x1E01BE0 (31464416)
- `dwGameEntitySystem_highestEntityIndex`: 0x20F0 (8432)
- `dwGameRules`: 0x1D1D4E0 (30528736)
- `dwGlobalVars`: 0x1AE9458 (28218456)
- `dwGlowManager`: 0x1D1D508 (30528776)
- `dwLocalPlayerController`: 0x1D10240 (30474816)
- `dwLocalPlayerPawn`: 0x1AF4B80 (28265344)
- `dwPlantedC4`: 0x1D27180 (30568832)
- `dwPrediction`: 0x1AF4AB0 (28265136)
- `dwSensitivity`: 0x1D1E018 (30531608)
- `dwSensitivity_sensitivity`: 0x48 (72)
- `dwViewAngles`: 0x1D2C7C0 (30590912)
- `dwViewMatrix`: 0x1D21A00 (30546432)
- `dwViewRender`: 0x1D22640 (30549568)
- `dwWeaponC4`: 0x1CC1888 (30152840)

#### engine2.dll 偏移
- `dwBuildNumber`: 0x5B94C4 (6001860)
- `dwNetworkGameClient`: 0x5B8B30 (5998000)
- `dwNetworkGameClient_clientTickCount`: 0x358 (856)
- `dwNetworkGameClient_deltaTick`: 0x23C (572)
- `dwNetworkGameClient_isBackgroundMap`: 0x2C1A0F (2888751)
- `dwNetworkGameClient_localPlayer`: 0xE8 (232)
- `dwNetworkGameClient_maxClients`: 0x230 (560)
- `dwNetworkGameClient_serverTickCount`: 0x23C (572)
- `dwNetworkGameClient_signOnState`: 0x220 (544)
- `dwWindowHeight`: 0x8A9DCC (9090268)
- `dwWindowWidth`: 0x8A9DC8 (9090264)

#### 其他模块偏移
- `inputsystem.dll::dwInputSystem`: 0x459F0 (285168)
- `matchmaking.dll::dwGameTypes`: 0x1B4010 (1785616)
- `soundsystem.dll::dwSoundSystem`: 0x3CF220 (3995168)
- `soundsystem.dll::dwSoundSystem_engineViewData`: 0x7C (124)

### 3. 构建号更新
- CS2 构建号更新为: **14094**
- 时间戳: 2025-08-07T06:57:19.301823800+00:00

### 4. 验证结果
- ✅ 所有偏移值已验证与 cs2-dumper 输出完全匹配
- ✅ 项目成功编译，无错误
- ✅ 程序可以正常运行
- ✅ 构建号正确设置

## 使用说明

### 运行程序
```bash
# 使用默认设置 (QEMU)
cargo run --release

# 使用 PCILeech 硬件
cargo run --release -- --connector pcileech

# 查看所有选项
cargo run --release -- --help
```

### 可用连接器
- `qemu` (默认)
- `kvm`
- `pcileech`
- `native`

### 注意事项
1. 确保安装了相应的 memflow 插件
2. 对于 QEMU/KVM 方法，需要设置虚拟机
3. 对于 PCILeech 方法，需要相应的硬件设备
4. 程序会在 http://localhost:8000 启动 Web 服务器

## 文件更改
- `build.rs`: 修改构建脚本以支持本地偏移文件
- `src/dma/cs2dumper/offsets_mod.rs`: 更新为最新偏移
- `src/dma/cs2dumper/client_mod.rs`: 更新为最新客户端定义
- `src/dma/cs2dumper/engine2_mod.rs`: 更新为最新引擎定义

## 下次更新
当需要更新偏移时，只需：
1. 运行 cs2-dumper 获取最新输出
2. 将输出文件放在 `../output/` 目录
3. 运行 `cargo build --release`

构建系统会自动检测并使用本地文件。
