[package]
name = "radarflow"
version = "0.2.5"
authors = ["Jane<PERSON> S <<EMAIL>"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# memory
memflow = "0.2.3"
memflow-native = { git = "https://github.com/memflow/memflow-native" }
dataview = "1.0.1"

# logging
log = "0.4.22"
simple_logger = "5.0.0"

# error handling
anyhow = "1.0.93"

# derive stuff
enum-primitive-derive = "0.3.0"
num-traits = "0.2.19"
serde = { version = "1.0.215", features = ["derive"] }
serde_json = "1.0.133"
clap = { version = "4.5.21", features = ["derive", "string"] }

# tokio
tokio = { version = "1.37.0", features = ["full"] }

# networking
axum = { version = "0.7.9", features = ["ws"] }
tower-http = { version = "0.6.2", features = ["fs"] }
tower = "0.5.1"
local-ip-address = "0.6.3"

# other
itertools = "0.13.0"


[build-dependencies]
reqwest = { version = "0.12.9", features = ["blocking"] }
serde = { version = "1.0.215", features = ["derive"] }
serde_json = "1.0.133"
vergen-gitcl = { version = "1.0.0", features = ["build", "cargo", "rustc",] }
