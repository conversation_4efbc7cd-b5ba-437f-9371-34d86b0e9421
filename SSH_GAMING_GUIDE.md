# SSH游戏中使用radarflow2指南

## 🎮 概述

这个指南将帮您设置通过SSH在游戏中快速控制radarflow2雷达的环境。您可以在CS2游戏中通过SSH命令快速启动、停止和监控雷达。

## 🚀 快速设置

### 1. 运行自动设置脚本
```bash
cd ~/radarflow
./scripts/setup_ssh_gaming.sh
```

这个脚本会自动：
- 设置所有必要的权限
- 创建快捷命令别名
- 优化SSH配置
- 创建超快速启动脚本

### 2. 重新加载配置
```bash
source ~/.bashrc
```

## 🎯 游戏中使用

### 超快速命令 (推荐)
```bash
# 检查虚拟机状态 (重要!)
vm

# 启动雷达 (最快)
r

# 重启雷达
rs

# 查看状态
st

# 停止雷达
radar-s

# 查看日志
rl

# 显示Web界面地址
radar-web
```

### SSH远程命令
假设您的Linux主机IP是 `*************`，用户名是 `user`：

```bash
# 从Windows游戏机SSH到Linux主机启动雷达
ssh user@************* 'r'

# 检查雷达状态
ssh user@************* 'st'

# 重启雷达
ssh user@************* 'rs'

# 停止雷达
ssh user@************* 'radar-s'
```

## 🖥️ 在Windows游戏中使用

### 方法1: PowerShell/CMD
在Windows中打开PowerShell或CMD：
```powershell
# 启动雷达
ssh user@************* "r"

# 查看状态
ssh user@************* "st"
```

### 方法2: Windows Terminal
1. 安装Windows Terminal
2. 创建快捷键配置
3. 设置一键SSH命令

### 方法3: 游戏内覆盖终端
使用如 `Windows Terminal Quake Mode` 或类似工具在游戏上方显示终端。

## 📱 手机控制 (可选)

### 使用SSH客户端App
1. **Android**: Termux, JuiceSSH
2. **iOS**: Terminus, Blink

### 示例 (Termux)
```bash
# 安装openssh
pkg install openssh

# 连接并启动雷达
ssh user@************* 'r'
```

## 🔧 高级配置

### 创建批处理文件 (Windows)
创建 `start_radar.bat`：
```batch
@echo off
ssh user@************* "r"
pause
```

### 创建桌面快捷方式
1. 右键桌面 → 新建 → 快捷方式
2. 位置：`cmd /c "ssh user@************* 'r' && pause"`
3. 命名为 "启动雷达"

### SSH密钥认证 (推荐)
```bash
# 在Windows上生成密钥
ssh-keygen -t rsa -b 4096

# 复制公钥到Linux主机
ssh-copy-id user@*************
```

## 🌐 Web界面访问

雷达启动后，可以通过浏览器访问：
```
http://*************:8000
```

## 🔍 故障排除

### 常见问题

1. **SSH连接被拒绝**
   ```bash
   # 在Linux主机上启动SSH服务
   sudo systemctl start ssh
   sudo systemctl enable ssh
   ```

2. **权限被拒绝**
   ```bash
   # 检查脚本权限
   ls -la ~/radarflow/scripts/
   chmod +x ~/radarflow/scripts/*.sh
   ```

3. **命令未找到**
   ```bash
   # 重新加载别名
   source ~/.bashrc
   source ~/.radarflow_aliases
   ```

4. **radarflow启动失败**
   ```bash
   # 查看详细日志
   rl
   # 或
   tail -f /tmp/radarflow.log
   ```

### 网络配置

确保防火墙允许相关端口：
```bash
# 允许SSH (端口22)
sudo ufw allow ssh

# 允许Web界面 (端口8000)
sudo ufw allow 8000
```

## 📋 命令速查表

| 命令 | 功能 | 完整命令 |
|------|------|----------|
| `vm` | 检查虚拟机 | `~/radarflow/scripts/start_radar.sh checkvm` |
| `r` | 启动雷达 | `~/radarflow/scripts/start_radar.sh start` |
| `rs` | 重启雷达 | `~/radarflow/scripts/start_radar.sh restart` |
| `st` | 查看状态 | `~/radarflow/scripts/start_radar.sh status` |
| `radar-s` | 停止雷达 | `~/radarflow/scripts/start_radar.sh stop` |
| `rl` | 查看日志 | `~/radarflow/scripts/start_radar.sh logs` |
| `radar-web` | Web地址 | `echo "http://$(hostname -I):8000"` |

## 🎮 游戏中的最佳实践

### 启动流程
1. **启动虚拟机** (如果未运行)：`virsh start win10-cs2`
2. **检查虚拟机状态**：`ssh user@host 'vm'`
3. **启动雷达**：`ssh user@host 'r'`
4. **检查雷达状态**：`ssh user@host 'st'`
5. **打开Web界面**：`http://host:8000`
6. **在虚拟机中启动CS2**

### 游戏中监控
- 使用第二个显示器显示Web界面
- 或使用手机/平板访问Web界面
- 通过SSH快速重启如果出现问题

### 游戏结束
- 可以保持雷达运行，或通过SSH停止：`ssh user@host 'radar-s'`

## 🔒 安全建议

1. **使用SSH密钥认证**而不是密码
2. **更改默认SSH端口**（可选）
3. **设置防火墙规则**限制访问
4. **定期更新系统**和SSH服务

## 📞 支持

如果遇到问题：
1. 查看日志：`rl` 或 `tail -f /tmp/radarflow.log`
2. 检查网络连接
3. 验证SSH服务状态
4. 确认防火墙设置

---

**提示**: 建议先在本地测试所有命令，确保一切正常后再在实际游戏中使用。
