use std::error::Error;

use serde::{Deserialize, Serialize};
use vergen_gitcl::{Emitter, GitclBuilder};

#[derive(Clone, Deserialize, Serialize)]
struct InfoJson {
    build_number: usize,
    timestamp: String
}

fn download(url: &str, to: &str) -> Result<(), Box<dyn Error>> {
    let content = reqwest::blocking::get(url)
        .unwrap_or_else(|_| panic!("Downloading \"{to}\""))
        .text()
        .expect("Convert response to text");

    std::fs::write(to, content)
    .expect("Write to file");

    Ok(())
}

fn build_number() -> Result<(), Box<dyn Error>> {
    let content = reqwest::blocking::get("https://raw.githubusercontent.com/a2x/cs2-dumper/main/output/info.json")
        .unwrap_or_else(|_| panic!("Downloading info.json"))
        .text()
        .expect("Convert response to text");

    let info = serde_json::from_str::<InfoJson>(&content)?;
    println!("cargo:rustc-env=CS2_BUILD_NUMBER={}", info.build_number);

    Ok(())
}

fn main() -> Result<(), Box<dyn Error>> {
    // Check if local files exist first, otherwise download from remote
    let local_client_path = "../output/client_dll.rs";
    let local_offsets_path = "../output/offsets.rs";
    let local_engine_path = "../output/engine2_dll.rs";
    let local_info_path = "../output/info.json";

    if std::path::Path::new(local_client_path).exists() {
        println!("Using local client_dll.rs");
        std::fs::copy(local_client_path, "./src/dma/cs2dumper/client_mod.rs")
            .expect("Failed to copy local client_dll.rs");
    } else {
        download(
            "https://raw.githubusercontent.com/a2x/cs2-dumper/refs/heads/main/output/client_dll.rs",
            "./src/dma/cs2dumper/client_mod.rs"
        ).expect("Failed to download build file \"client.dll.rs\"");
    }

    if std::path::Path::new(local_offsets_path).exists() {
        println!("Using local offsets.rs");
        std::fs::copy(local_offsets_path, "./src/dma/cs2dumper/offsets_mod.rs")
            .expect("Failed to copy local offsets.rs");
    } else {
        download(
            "https://raw.githubusercontent.com/a2x/cs2-dumper/refs/heads/main/output/offsets.rs",
            "./src/dma/cs2dumper/offsets_mod.rs"
        ).expect("Failed to download build file \"offsets.rs\"");
    }

    if std::path::Path::new(local_engine_path).exists() {
        println!("Using local engine2_dll.rs");
        std::fs::copy(local_engine_path, "./src/dma/cs2dumper/engine2_mod.rs")
            .expect("Failed to copy local engine2_dll.rs");
    } else {
        download(
            "https://raw.githubusercontent.com/a2x/cs2-dumper/refs/heads/main/output/engine2_dll.rs",
            "./src/dma/cs2dumper/engine2_mod.rs"
        ).expect("Failed to download build file \"engine2.dll.rs\"");
    }

    // Handle build number
    if std::path::Path::new(local_info_path).exists() {
        println!("Using local info.json for build number");
        let content = std::fs::read_to_string(local_info_path)
            .expect("Failed to read local info.json");
        let info = serde_json::from_str::<InfoJson>(&content)?;
        println!("cargo:rustc-env=CS2_BUILD_NUMBER={}", info.build_number);
    } else {
        build_number()?;
    }

    let gitcl = GitclBuilder::all_git()?;

    Emitter::new()
        .add_instructions(&gitcl)?
        .emit()?;

    Ok(())
}