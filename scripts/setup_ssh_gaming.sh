#!/bin/bash

# SSH游戏环境设置脚本
# 用于配置便于游戏中使用的SSH环境

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置脚本权限
setup_permissions() {
    print_status "设置脚本执行权限..."
    chmod +x "$SCRIPT_DIR/start_radar.sh"
    chmod +x "$SCRIPT_DIR/quick_commands.sh"
    chmod +x "$SCRIPT_DIR/setup_ssh_gaming.sh"
    print_success "权限设置完成"
}

# 创建别名配置
setup_aliases() {
    print_status "设置命令别名..."
    
    local alias_file="$HOME/.radarflow_aliases"
    
    cat > "$alias_file" << 'EOF'
# radarflow2 快速命令别名
# 由 setup_ssh_gaming.sh 自动生成

# 基本命令
alias radar-start='~/radarflow/scripts/start_radar.sh start'
alias radar-stop='~/radarflow/scripts/start_radar.sh stop'
alias radar-restart='~/radarflow/scripts/start_radar.sh restart'
alias radar-status='~/radarflow/scripts/start_radar.sh status'
alias radar-logs='~/radarflow/scripts/start_radar.sh logs'

# 超短别名 (游戏中快速使用)
alias r='~/radarflow/scripts/start_radar.sh start'
alias rs='~/radarflow/scripts/start_radar.sh restart'
alias st='~/radarflow/scripts/start_radar.sh status'
alias rl='~/radarflow/scripts/start_radar.sh logs'
alias vm='~/radarflow/scripts/start_radar.sh checkvm'

# 停止命令 (避免与系统命令冲突)
alias radar-s='~/radarflow/scripts/start_radar.sh stop'

# 快速导航
alias cdradar='cd ~/radarflow'

# 快速构建
alias radar-build='cd ~/radarflow && cargo build --release'

# 查看Web界面地址
alias radar-web='echo "Web界面: http://$(hostname -I | awk "{print \$1}"):8000"'
EOF

    # 添加到 .bashrc
    if ! grep -q "source.*radarflow_aliases" "$HOME/.bashrc"; then
        echo "" >> "$HOME/.bashrc"
        echo "# radarflow2 别名" >> "$HOME/.bashrc"
        echo "source $alias_file" >> "$HOME/.bashrc"
        print_success "别名已添加到 .bashrc"
    else
        print_warning "别名已存在于 .bashrc"
    fi
    
    # 立即加载别名
    source "$alias_file"
    print_success "别名设置完成"
}

# 创建快速启动脚本
create_quick_script() {
    print_status "创建快速启动脚本..."
    
    local quick_script="$HOME/r"
    cat > "$quick_script" << EOF
#!/bin/bash
# 超快速启动脚本 - 只需输入 ~/r
exec $SCRIPT_DIR/start_radar.sh start
EOF
    chmod +x "$quick_script"
    
    print_success "快速启动脚本已创建: ~/r"
}

# 设置SSH配置优化
setup_ssh_config() {
    print_status "优化SSH配置..."
    
    local ssh_config="$HOME/.ssh/config"
    
    # 创建.ssh目录如果不存在
    mkdir -p "$HOME/.ssh"
    chmod 700 "$HOME/.ssh"
    
    # 检查是否已有优化配置
    if ! grep -q "# radarflow SSH优化" "$ssh_config" 2>/dev/null; then
        cat >> "$ssh_config" << 'EOF'

# radarflow SSH优化配置
Host *
    # 保持连接活跃
    ServerAliveInterval 60
    ServerAliveCountMax 3
    
    # 快速连接
    ControlMaster auto
    ControlPath ~/.ssh/control-%r@%h:%p
    ControlPersist 10m
    
    # 压缩传输
    Compression yes
EOF
        print_success "SSH配置已优化"
    else
        print_warning "SSH配置已存在"
    fi
}

# 创建游戏专用配置
create_gaming_config() {
    print_status "创建游戏专用配置..."
    
    local config_file="$PROJECT_DIR/gaming_config.toml"
    cat > "$config_file" << 'EOF'
# radarflow2 游戏专用配置

[server]
port = 8000
web_path = "./webradar"

[dma]
connector = "qemu"
loglevel = "info"
skip_version = false

[gaming]
# 游戏中使用的快捷键提示
shortcuts = [
    "r - 启动雷达",
    "rs - 重启雷达", 
    "st - 查看状态",
    "radar-s - 停止雷达",
    "rl - 查看日志"
]

# SSH命令示例
ssh_examples = [
    "ssh user@host 'r'",
    "ssh user@host 'st'",
    "ssh user@host 'rs'"
]
EOF
    
    print_success "游戏配置文件已创建: $config_file"
}

# 显示使用说明
show_usage() {
    print_success "=== SSH游戏环境设置完成 ==="
    echo ""
    print_status "可用的命令："
    echo "  r              - 启动雷达 (最快)"
    echo "  rs             - 重启雷达"
    echo "  st             - 查看状态"
    echo "  radar-s        - 停止雷达"
    echo "  rl             - 查看日志"
    echo "  radar-web      - 显示Web界面地址"
    echo ""
    print_status "SSH远程使用示例："
    echo "  ssh user@$(hostname -I | awk '{print $1}') 'r'"
    echo "  ssh user@$(hostname -I | awk '{print $1}') 'st'"
    echo "  ssh user@$(hostname -I | awk '{print $1}') 'rs'"
    echo ""
    print_status "超快速启动："
    echo "  ~/r            - 直接运行"
    echo ""
    print_warning "注意："
    echo "  - 请确保SSH服务已启动: sudo systemctl start ssh"
    echo "  - 请确保防火墙允许SSH连接"
    echo "  - 建议设置SSH密钥认证以提高安全性"
    echo ""
    print_status "重新加载配置："
    echo "  source ~/.bashrc"
}

# 检查依赖
check_dependencies() {
    print_status "检查依赖..."
    
    # 检查SSH服务
    if ! systemctl is-active --quiet ssh; then
        print_warning "SSH服务未运行，尝试启动..."
        if sudo systemctl start ssh 2>/dev/null; then
            print_success "SSH服务已启动"
        else
            print_error "无法启动SSH服务，请手动启动: sudo systemctl start ssh"
        fi
    else
        print_success "SSH服务正在运行"
    fi
    
    # 检查radarflow二进制文件
    if [ ! -f "$PROJECT_DIR/target/release/radarflow" ]; then
        print_warning "radarflow二进制文件不存在，需要先构建"
        print_status "运行: cd $PROJECT_DIR && cargo build --release"
    else
        print_success "radarflow二进制文件存在"
    fi
}

# 主函数
main() {
    echo "=== radarflow2 SSH游戏环境设置 ==="
    echo ""
    
    check_dependencies
    setup_permissions
    setup_aliases
    create_quick_script
    setup_ssh_config
    create_gaming_config
    
    echo ""
    show_usage
}

# 运行主函数
main "$@"
