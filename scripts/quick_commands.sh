#!/bin/bash

# 快速命令脚本 - 用于游戏中快速执行
# 这些是简化的单行命令，方便在游戏中通过SSH快速执行

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RADAR_SCRIPT="$SCRIPT_DIR/start_radar.sh"

# 快速启动 (别名: r, radar, start)
r() {
    "$RADAR_SCRIPT" start
}

radar() {
    "$RADAR_SCRIPT" start
}

start() {
    "$RADAR_SCRIPT" start
}

# 快速停止 (别名: s, stop)
s() {
    "$RADAR_SCRIPT" stop
}

stop() {
    "$RADAR_SCRIPT" stop
}

# 快速重启 (别名: rs, restart)
rs() {
    "$RADAR_SCRIPT" restart
}

restart() {
    "$RADAR_SCRIPT" restart
}

# 快速状态检查 (别名: st, status)
st() {
    "$RADAR_SCRIPT" status
}

status() {
    "$RADAR_SCRIPT" status
}

# 快速查看日志 (别名: l, logs)
l() {
    "$RADAR_SCRIPT" logs
}

logs() {
    "$RADAR_SCRIPT" logs
}

# 导出函数，使其在当前shell中可用
export -f r radar start s stop rs restart st status l logs

# 如果直接运行此脚本，显示可用命令
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "快速命令已加载！可用的命令："
    echo ""
    echo "启动雷达:"
    echo "  r, radar, start"
    echo ""
    echo "停止雷达:"
    echo "  s, stop"
    echo ""
    echo "重启雷达:"
    echo "  rs, restart"
    echo ""
    echo "查看状态:"
    echo "  st, status"
    echo ""
    echo "查看日志:"
    echo "  l, logs"
    echo ""
    echo "使用方法："
    echo "  source $0  # 加载命令到当前shell"
    echo "  然后直接输入: r  # 启动雷达"
fi
