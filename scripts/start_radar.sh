#!/bin/bash

# radarflow2 启动脚本
# 用于通过SSH远程启动雷达

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BINARY_PATH="$PROJECT_DIR/target/release/radarflow"
PID_FILE="/tmp/radarflow.pid"
LOG_FILE="/tmp/radarflow.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否已经在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    fi
    return 1  # 未运行
}

# 检查QEMU虚拟机是否运行
check_qemu() {
    if pgrep -f "qemu.*win" > /dev/null; then
        return 0  # QEMU正在运行
    else
        return 1  # QEMU未运行
    fi
}

# 启动radarflow
start_radar() {
    print_status "检查radarflow状态..."

    if check_running; then
        print_warning "radarflow已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi

    # 检查二进制文件是否存在
    if [ ! -f "$BINARY_PATH" ]; then
        print_error "找不到radarflow二进制文件: $BINARY_PATH"
        print_status "请先运行: cargo build --release"
        return 1
    fi

    # 检查QEMU虚拟机是否运行
    print_status "检查QEMU虚拟机状态..."
    if ! check_qemu; then
        print_error "未找到运行中的QEMU虚拟机!"
        print_status "请先启动您的Windows 10虚拟机"
        print_status "提示: 确保虚拟机名称包含 'win' 关键字"
        return 1
    fi
    print_success "找到运行中的QEMU虚拟机"

    print_status "启动radarflow..."

    # 启动radarflow (后台运行)
    cd "$PROJECT_DIR"
    nohup "$BINARY_PATH" --connector qemu --loglevel info > "$LOG_FILE" 2>&1 &
    local pid=$!

    # 保存PID
    echo "$pid" > "$PID_FILE"

    # 等待一下确保启动成功
    sleep 3

    if check_running; then
        print_success "radarflow启动成功!"
        print_status "PID: $pid"
        print_status "日志文件: $LOG_FILE"
        print_status "Web界面: http://$(hostname -I | awk '{print $1}'):8000"
        return 0
    else
        print_error "radarflow启动失败"
        if [ -f "$LOG_FILE" ]; then
            print_status "错误日志:"
            tail -10 "$LOG_FILE"
        fi
        return 1
    fi
}

# 停止radarflow
stop_radar() {
    print_status "停止radarflow..."
    
    if ! check_running; then
        print_warning "radarflow未在运行"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    kill "$pid"
    
    # 等待进程结束
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p "$pid" > /dev/null 2>&1; then
        print_warning "进程未正常结束，强制终止..."
        kill -9 "$pid"
    fi
    
    rm -f "$PID_FILE"
    print_success "radarflow已停止"
}

# 重启radarflow
restart_radar() {
    print_status "重启radarflow..."
    stop_radar
    sleep 1
    start_radar
}

# 查看状态
status_radar() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        print_success "radarflow正在运行 (PID: $pid)"
        
        # 显示内存使用情况
        local mem_usage=$(ps -p "$pid" -o rss= 2>/dev/null)
        if [ -n "$mem_usage" ]; then
            print_status "内存使用: $((mem_usage / 1024)) MB"
        fi
        
        # 显示Web界面地址
        print_status "Web界面: http://$(hostname -I | awk '{print $1}'):8000"
        
        return 0
    else
        print_warning "radarflow未在运行"
        return 1
    fi
}

# 查看日志
logs_radar() {
    if [ -f "$LOG_FILE" ]; then
        print_status "显示最近的日志 (按Ctrl+C退出):"
        tail -f "$LOG_FILE"
    else
        print_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 检查虚拟机状态
check_vm() {
    print_status "检查QEMU虚拟机状态..."

    local qemu_processes=$(pgrep -f "qemu" | wc -l)
    if [ "$qemu_processes" -gt 0 ]; then
        print_success "找到 $qemu_processes 个QEMU进程"

        # 显示QEMU进程详情
        print_status "QEMU进程详情:"
        ps aux | grep qemu | grep -v grep | while read line; do
            echo "  $line"
        done

        # 检查是否有Windows虚拟机
        if pgrep -f "qemu.*win" > /dev/null; then
            print_success "找到Windows虚拟机进程"
        else
            print_warning "未找到Windows虚拟机进程 (进程名不包含'win')"
        fi
    else
        print_error "未找到QEMU进程"
        print_status "请先启动您的虚拟机"
    fi
}

# 显示帮助
show_help() {
    echo "radarflow2 控制脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|checkvm|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动radarflow"
    echo "  stop    - 停止radarflow"
    echo "  restart - 重启radarflow"
    echo "  status  - 查看运行状态"
    echo "  logs    - 查看实时日志"
    echo "  checkvm - 检查虚拟机状态"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 checkvm  # 检查虚拟机"
    echo "  $0 start    # 启动雷达"
    echo "  $0 status   # 查看状态"
    echo "  $0 stop     # 停止雷达"
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start_radar
            ;;
        stop)
            stop_radar
            ;;
        restart)
            restart_radar
            ;;
        status)
            status_radar
            ;;
        logs)
            logs_radar
            ;;
        checkvm|vm)
            check_vm
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            print_error "请指定操作命令"
            show_help
            exit 1
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
