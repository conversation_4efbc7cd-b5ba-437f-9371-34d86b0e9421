# 完整使用示例

## 🎯 场景描述

您有一台Linux主机运行KVM虚拟机，想要在Windows游戏机上通过SSH控制radarflow2雷达。

**环境设置:**
- Linux主机: ************* (运行KVM虚拟机)
- Windows游戏机: ************* (运行CS2)
- 虚拟机: win10-cs2 (在Linux主机上)

## 🚀 完整操作流程

### 1. 初始设置 (一次性)

在Linux主机上运行:
```bash
cd ~/radarflow
./scripts/setup_ssh_gaming.sh
source ~/.bashrc
```

### 2. 启动虚拟机

在Linux主机上:
```bash
# 启动虚拟机
virsh start win10-cs2

# 或使用图形界面
virt-manager
```

### 3. 从Windows游戏机控制

#### 方法A: 直接SSH命令
```powershell
# 在Windows PowerShell中

# 1. 检查虚拟机状态
ssh user@************* "vm"

# 2. 启动雷达
ssh user@************* "r"

# 3. 检查雷达状态
ssh user@************* "st"
```

#### 方法B: 创建批处理文件

创建 `radar_control.bat`:
```batch
@echo off
echo === radarflow2 控制面板 ===
echo.
echo 1. 检查虚拟机状态
echo 2. 启动雷达
echo 3. 查看雷达状态
echo 4. 重启雷达
echo 5. 停止雷达
echo 6. 查看日志
echo 0. 退出
echo.

set /p choice="请选择操作 (0-6): "

if "%choice%"=="1" ssh user@************* "vm"
if "%choice%"=="2" ssh user@************* "r"
if "%choice%"=="3" ssh user@************* "st"
if "%choice%"=="4" ssh user@************* "rs"
if "%choice%"=="5" ssh user@************* "radar-s"
if "%choice%"=="6" ssh user@************* "rl"
if "%choice%"=="0" exit

pause
goto :eof
```

### 4. 游戏中快速操作

#### 创建桌面快捷方式

**启动雷达.lnk:**
- 目标: `cmd /c "ssh user@************* 'r' && pause"`
- 图标: 选择一个雷达图标

**查看状态.lnk:**
- 目标: `cmd /c "ssh user@************* 'st' && pause"`

**重启雷达.lnk:**
- 目标: `cmd /c "ssh user@************* 'rs' && pause"`

#### 使用Windows Terminal

配置Windows Terminal快捷键:
```json
{
    "command": {
        "action": "newTab",
        "commandline": "ssh user@*************"
    },
    "keys": "ctrl+shift+r"
}
```

## 📱 实际游戏场景

### 场景1: 游戏开始前
```bash
# 在Windows上打开PowerShell
# 1. 检查环境
ssh user@************* "vm"
# 输出: [SUCCESS] 找到Windows虚拟机进程

# 2. 启动雷达
ssh user@************* "r"
# 输出: [SUCCESS] radarflow启动成功!

# 3. 打开浏览器访问
# http://*************:8000
```

### 场景2: 游戏中雷达异常
```bash
# 快速重启雷达
ssh user@************* "rs"
```

### 场景3: 查看雷达状态
```bash
# 检查是否正常运行
ssh user@************* "st"
```

### 场景4: 游戏结束
```bash
# 停止雷达 (可选)
ssh user@************* "radar-s"
```

## 🔧 高级技巧

### 1. 一键启动脚本

创建 `start_all.bat`:
```batch
@echo off
echo 启动完整游戏环境...

echo 1. 检查虚拟机...
ssh user@************* "vm"
if errorlevel 1 (
    echo 虚拟机未运行，请先启动虚拟机
    pause
    exit
)

echo 2. 启动雷达...
ssh user@************* "r"

echo 3. 打开Web界面...
start http://*************:8000

echo 环境启动完成！
pause
```

### 2. 状态监控脚本

创建 `monitor.bat`:
```batch
@echo off
:loop
cls
echo === radarflow2 状态监控 ===
echo 时间: %date% %time%
echo.
ssh user@************* "st"
echo.
echo 按Ctrl+C退出，或等待10秒自动刷新...
timeout /t 10 /nobreak >nul
goto loop
```

### 3. 使用PowerShell函数

在PowerShell配置文件中添加:
```powershell
# 编辑配置文件
notepad $PROFILE

# 添加以下函数
function Start-Radar { ssh user@************* "r" }
function Stop-Radar { ssh user@************* "radar-s" }
function Restart-Radar { ssh user@************* "rs" }
function Get-RadarStatus { ssh user@************* "st" }
function Get-VMStatus { ssh user@************* "vm" }

# 使用别名
Set-Alias -Name radar-start -Value Start-Radar
Set-Alias -Name radar-stop -Value Stop-Radar
Set-Alias -Name radar-restart -Value Restart-Radar
Set-Alias -Name radar-status -Value Get-RadarStatus
Set-Alias -Name vm-status -Value Get-VMStatus
```

然后可以直接使用:
```powershell
radar-start
radar-status
vm-status
```

## 📱 手机控制 (额外功能)

### 使用Termux (Android)
```bash
# 安装openssh
pkg install openssh

# 创建快捷脚本
echo 'ssh user@************* "r"' > ~/start_radar
chmod +x ~/start_radar

# 使用
./start_radar
```

### 使用Shortcuts (iOS)
创建快捷指令:
1. 添加"运行SSH脚本"操作
2. 服务器: *************
3. 脚本: r

## 🔍 故障排除示例

### 问题1: 连接被拒绝
```bash
# 检查SSH服务
ssh user@************* "systemctl status ssh"

# 检查防火墙
ssh user@************* "sudo ufw status"
```

### 问题2: 虚拟机未运行
```bash
# 检查虚拟机状态
ssh user@************* "virsh list --all"

# 启动虚拟机
ssh user@************* "virsh start win10-cs2"
```

### 问题3: 雷达启动失败
```bash
# 查看详细日志
ssh user@************* "rl"

# 检查进程
ssh user@************* "ps aux | grep radarflow"
```

## 📊 性能监控

### 创建监控脚本
```batch
@echo off
echo === 系统状态监控 ===

echo 1. 虚拟机状态:
ssh user@************* "vm"

echo.
echo 2. 雷达状态:
ssh user@************* "st"

echo.
echo 3. 系统资源:
ssh user@************* "free -h && df -h | head -5"

echo.
echo 4. 网络连接:
ping -n 1 ************* | find "TTL"

pause
```

## 🎮 最佳实践总结

1. **游戏前检查清单:**
   - 虚拟机运行: `vm`
   - 启动雷达: `r`
   - 检查状态: `st`
   - 打开Web界面

2. **游戏中监控:**
   - 使用第二显示器显示Web界面
   - 准备快速重启命令: `rs`

3. **游戏后清理:**
   - 可选停止雷达: `radar-s`
   - 保持虚拟机运行以备下次使用

4. **安全建议:**
   - 使用SSH密钥认证
   - 配置防火墙规则
   - 定期更新系统

---

**提示**: 建议先在测试环境中练习所有操作，确保熟悉后再在实际游戏中使用。
