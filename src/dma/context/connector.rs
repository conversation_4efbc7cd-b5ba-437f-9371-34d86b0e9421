#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON>q, <PERSON>ial<PERSON>rd, Or<PERSON>, clap::ValueEnum, Default)]
pub enum Connector {
    #[default]
    Qemu,
    Kvm,
    <PERSON><PERSON>lee<PERSON>,
    Native
}

impl ToString for Connector {
    fn to_string(&self) -> String {
        match self {
            Connector::Qemu => String::from("qemu"),
            Connector::Kvm => String::from("kvm"),
            Connector::Pcileech => String::from("pcileech"),
            Connector::Native => String::from("native"),
        }
    }
}
