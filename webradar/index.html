<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>radarflow</title>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <div id="canvasContainer">
        <div id="settingsHolder">
            <div class="settings">
                <div>
                    <input type="checkbox" onclick="toggleZoom()" id="zoomCheck" name="zoom"/>
                    <label for="zoomCheck">Zoom</label>
                </div>
                <div>
                    <input type="checkbox" onclick="toggleStats()" id="statsCheck" name="stats"/>
                    <label for="statsCheck">Stats</label>
                </div>
            </div>
        </div>
        <canvas id="canvas"></canvas>
    </div>
    <script src="script.js"></script>
</body>
</html>