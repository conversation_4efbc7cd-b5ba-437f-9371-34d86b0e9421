# KVM/QEMU虚拟机设置指南

## 🎯 概述

这个指南将帮助您正确设置KVM/QEMU虚拟机以配合radarflow2使用。

## 📋 前提条件

### 1. 安装必要软件
```bash
# 安装KVM和QEMU
sudo apt update
sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager

# 安装memflow插件
# 使用memflowup安装 memflow-qemu 和 memflow-win32
```

### 2. 用户权限设置
```bash
# 将用户添加到libvirt组
sudo usermod -a -G libvirt $USER
sudo usermod -a -G kvm $USER

# 重新登录或重启以使权限生效
```

## 🖥️ 虚拟机配置

### 1. 创建Windows 10虚拟机

#### 使用virt-manager (图形界面)
```bash
# 启动虚拟机管理器
virt-manager
```

#### 使用命令行
```bash
# 创建虚拟机 (示例)
virt-install \
  --name win10-cs2 \
  --ram 8192 \
  --disk path=/var/lib/libvirt/images/win10-cs2.qcow2,size=60 \
  --vcpus 4 \
  --os-type windows \
  --os-variant win10 \
  --network bridge=virbr0 \
  --graphics vnc,listen=0.0.0.0 \
  --console pty,target_type=serial \
  --cdrom /path/to/windows10.iso
```

### 2. 重要配置要求

#### 虚拟机名称
- 确保虚拟机名称包含 "win" 关键字
- 推荐名称: `win10-cs2`, `windows10`, `win-gaming` 等

#### 内存配置
- 最少 8GB RAM (推荐 16GB)
- 为CS2游戏预留足够内存

#### CPU配置
- 最少 4 核心 (推荐 6-8 核心)
- 启用CPU直通以提高性能

#### 网络配置
- 使用桥接网络以便访问Web界面
- 确保虚拟机可以访问互联网

## 🚀 启动虚拟机

### 方法1: 使用virsh命令
```bash
# 列出所有虚拟机
virsh list --all

# 启动虚拟机
virsh start win10-cs2

# 检查状态
virsh list
```

### 方法2: 使用virt-manager
```bash
# 启动图形管理器
virt-manager

# 在界面中右键虚拟机 → 运行
```

### 方法3: 自动启动脚本
创建 `start_vm.sh`:
```bash
#!/bin/bash
VM_NAME="win10-cs2"  # 替换为您的虚拟机名称

echo "启动虚拟机: $VM_NAME"
virsh start "$VM_NAME"

echo "等待虚拟机启动..."
sleep 10

echo "检查虚拟机状态:"
virsh list | grep "$VM_NAME"
```

## 🔍 验证设置

### 1. 检查虚拟机是否运行
```bash
# 使用radarflow脚本检查
./scripts/start_radar.sh checkvm

# 或手动检查
ps aux | grep qemu
virsh list
```

### 2. 检查memflow连接
```bash
# 测试memflow连接
./target/release/radarflow --connector qemu --help
```

## 🎮 游戏设置流程

### 完整启动流程
1. **启动虚拟机**
   ```bash
   virsh start win10-cs2
   # 或使用您的启动脚本
   ```

2. **验证虚拟机运行**
   ```bash
   ./scripts/start_radar.sh checkvm
   ```

3. **启动radarflow**
   ```bash
   ./scripts/start_radar.sh start
   # 或使用别名: r
   ```

4. **在虚拟机中启动CS2**
   - 通过VNC或RDP连接到虚拟机
   - 启动Steam和CS2

5. **访问Web界面**
   ```
   http://您的主机IP:8000
   ```

## 🛠️ 故障排除

### 常见问题

#### 1. "未找到QEMU进程"
```bash
# 检查虚拟机状态
virsh list --all

# 启动虚拟机
virsh start 您的虚拟机名称
```

#### 2. "找不到Windows虚拟机进程"
- 确保虚拟机名称包含 "win" 关键字
- 或修改脚本中的检测逻辑

#### 3. memflow连接失败
```bash
# 检查memflow插件
memflowup list

# 重新安装插件
memflowup install memflow-qemu
memflowup install memflow-win32
```

#### 4. 权限问题
```bash
# 检查用户组
groups $USER

# 重新添加到组
sudo usermod -a -G libvirt,kvm $USER
```

### 性能优化

#### 1. CPU优化
```xml
<!-- 在虚拟机XML配置中 -->
<cpu mode='host-passthrough' check='none'/>
```

#### 2. 内存优化
```xml
<!-- 启用大页内存 -->
<memoryBacking>
  <hugepages/>
</memoryBacking>
```

#### 3. 磁盘优化
```bash
# 使用SSD存储虚拟机镜像
# 启用磁盘缓存
```

## 📝 自动化脚本

### 创建一键启动脚本
```bash
#!/bin/bash
# 文件: start_gaming.sh

VM_NAME="win10-cs2"

echo "=== 启动游戏环境 ==="

# 1. 启动虚拟机
echo "启动虚拟机..."
virsh start "$VM_NAME"
sleep 15

# 2. 检查虚拟机状态
echo "检查虚拟机状态..."
if ! virsh list | grep -q "$VM_NAME.*running"; then
    echo "错误: 虚拟机启动失败"
    exit 1
fi

# 3. 启动radarflow
echo "启动radarflow..."
cd ~/radarflow
./scripts/start_radar.sh start

echo "=== 游戏环境启动完成 ==="
echo "虚拟机: $VM_NAME"
echo "Web界面: http://$(hostname -I | awk '{print $1}'):8000"
```

### SSH远程启动
```bash
# 从游戏机SSH启动整个环境
ssh user@linux-host './start_gaming.sh'
```

## 🔒 安全建议

1. **网络隔离**: 使用专用网络桥接
2. **防火墙**: 配置适当的防火墙规则
3. **访问控制**: 限制Web界面访问
4. **定期更新**: 保持系统和软件更新

## 📞 支持

如果遇到问题:
1. 检查虚拟机日志: `virsh dumpxml 虚拟机名称`
2. 查看QEMU日志: `journalctl -u libvirtd`
3. 检查memflow日志: `./scripts/start_radar.sh logs`

---

**提示**: 建议在专用的游戏主机上设置此环境，以获得最佳性能。
